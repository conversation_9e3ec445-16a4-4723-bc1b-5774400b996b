### **驾考通途智慧住宿管理系统 - 功能架构详解**

#### **一、 核心业务层**

**1. 登记与录入模块**
*   **核心目标**：通过智能化流程和硬件集成，极速、准确地完成人员信息登记，建立教练-学员关系。
*   **详细功能**：
    *   **教练信息智能登记**：
        *   提供搜索框，支持按姓名、身份证号、手机号快速检索现有教练库。
        *   选中检索结果后，自动填充教练信息，无需重复录入。
        *   若为新教练，连接**ZKTime ID180阅读器**刷身份证，自动填充姓名、性别、身份证号、地址等字段。
        *   手动补充录入教练手机号等非身份证信息，完成新建。
    *   **学员信息批量快录**：
        *   在已创建的教练订单下，使用身份证阅读器依次刷取学员身份证。
        *   系统自动读取并创建学员档案，实时添加到当前订单的学员列表中。
        *   支持在列表中对自动录入的信息进行手动修改和补全。
    *   **绑定关系确认**：系统自动将当前列表中的所有学员与主教练订单绑定，形成一个完整的住宿组。

**2. 智能分配与调整模块**
*   **核心目标**：基于动态房间性别规则，自动化、合规地分配床位，并提供直观的手动干预界面。
*   **详细功能**：
    *   **一键智能分配**：
        *   **算法执行**：识别当前订单内所有人员的性别 -> 若存在混合性别则自动拆分为男、女子组 -> 优先寻找同性别“已锁定”房间的空床位 -> 其次将人员分配至“未分配”房间（分配成功瞬间锁定房间性别）。
        *   **结果展示**：以可视化方式展示分配结果，清晰标明每个人被分配到的楼栋、房间及床位号。
    *   **手动拖拽调整**：
        *   在图形化房态图上，管理员可通过拖拽方式调整人员的床位。
        *   系统进行实时合规校验：拖拽至“未分配”房间则允许并锁定房间性别；拖拽至“已锁定”房间则仅当性别匹配时才允许。
    *   **提交入住与最终校验**：
        *   点击“提交入住”后，系统触发最终强制扫描。
        *   校验规则：确保所有已分配床位中，无一房间存在性别混合情况。
        *   系统拦截：若校验失败，提示具体冲突位置并阻止提交，必须调整至完全合规为止。

**3. 入住与财务管理模块**
*   **核心目标**：完成入住手续，处理费用流程，形成财务闭环。
*   **详细功能**：
    *   **统一结算（按教练）**：以教练为主账户，生成其名下所有学员的床位费用总账单。
    *   **多支付方式收款**：支持现金、银行卡、支付宝、微信支付等多种收款方式，并记录明细。
    *   **发票开具**：可根据结算金额，申请并开具电子或纸质发票。

**4. 退房与状态重置模块**
*   **核心目标**：高效办理退房，并智能释放和重置资源状态。
*   **详细功能**：
    *   **组退房功能**：支持一键选中同一教练订单下的多名学员，批量办理退房手续。
    *   **房间状态智能重置**：系统自动检测，当某房间内的**最后一位住客**完成退房后，自动将该房间的“房间性别状态”从“已锁定”重置为“**未分配**”，以供后续任意性别人员入住。

---

#### **二、 数据、支撑与可视化层**

**5. 数据库连接信息**
*   **数据库配置**
*   **注意：数据库root用户密码为：mysql_EmrBwc，使用该信息为项目新建专用的mysql用户**
    DB_HOST=localhost
    DB_PORT=3306
    DB_USER=
    DB_PASSWORD=
    DB_NAME=accommodation
    DB_CHARSET=utf8mb4
    DB_MAX_IDLE_CONNS=10
    DB_MAX_OPEN_CONNS=100
    DB_CONN_MAX_LIFETIME=3600s

**6. 房态可视化中心**
*   **核心目标**：为管理员提供全局、实时、图形化的运营监控视图。
*   **详细功能**：
    *   **楼层平面图视图**：按楼栋、楼层组织导航，直观展示整个住宿区的布局。
    *   **房间状态块**：每个房间为一个可视化单元，显示：房间号、当前性别状态（通过颜色/图标区分“未分配/男/女”）、实时入住比例（如：3/4）。
    *   **床位详情钻取**：点击任意房间，可弹出浮层查看其内每个床位的详细信息（状态、入住人姓名、所属教练、入住日期）。

**7. 客户与关系管理模块**
*   **核心目标**：积累客户数据，维护客户关系，支持业务查询。
*   **详细功能**：
    *   **教练主档案库**：存储所有教练信息，支持高级查询、筛选和信息编辑。
    *   **学员子档案库**：由历史入住订单自动积累而成，形成学员数据库。
    *   **历史订单查询**：可按教练姓名、学员姓名、身份证号、日期等多维度查询历史入住记录和详单。

**8. 经营分析报表模块**
*   **核心目标**：多维度分析经营数据，为管理决策提供数据支持。
*   **详细功能**：
    *   **日报/月报/年报**：自动生成关键指标报表，包括：营收、床位均价、入住率、客源人数等。
    *   **渠道（教练）分析**：分析不同教练带来的学员数量、消费金额，评估重要客源。
    *   **房源分析**：按楼栋、楼层、房型等多维度分析床位利用率和收益情况。

---

#### **三、 系统基础配置层**

**9. 组织与资源管理模块**
*   **核心目标**：定义和管理所有物理资源及其属性，是系统运行的基石。
*   **详细功能**：
    *   **楼栋与楼层管理**：创建并管理物理架构，如“1号楼”、“2号楼”，“1楼”、“2楼”。
    *   **房型模板库**：预定义标准房型（如“标准四人间”），设置其**床位数量、预设设施（空调/卫浴）、床位编号规则、参考图**。
    *   **房间管理**：基于所选房型模板，快速创建具体房间（如“101房”），并可对模板信息进行覆盖式微调。
    *   **床位管理**：查看由房间自动生成的所有床位列表，并可对床位编号等属性进行最终调整。

**10. 价格与规则体系模块**
*   **核心目标**：配置系统的计费规则和核心业务规则。
*   **详细功能**：
    *   **价格管理**：设置基础床位价格，可关联到**房型**；设置节假日、周末等特殊日期的价格。
    *   **规则配置**：维护和确认“动态房间性别锁定与重置”等核心业务规则的开关与参数。

**11. 用户与权限管理模块**
*   **核心目标**：确保系统安全，控制不同角色人员的操作权限。
*   **详细功能**：
    *   **角色管理**：定义角色（如：超级管理员、前台接待、财务），并为每个角色配置细粒度的功能操作权限（如：财务角色仅可查看报表和流水）。
    *   **用户管理**：创建系统登录账号，为用户分配角色。

**12. 外设与接口管理模块**
*   **核心目标**：管理和集成外部硬件设备，确保其正常工作。
*   **详细功能**：
    *   **身份证阅读器配置**：配置ZKTime ID180等设备的参数、驱动，提供“测试连接”功能以验证设备状态。
    *   **（预留）**：为未来可能集成的门锁、闸机、打印机等设备提供接口管理能力。

---
**总结**：此功能架构规划详尽地描述了每个模块的构成、目标和具体功能点，确保了开发团队、管理者和使用者都能对系统有清晰、一致的理解，为项目的成功实施奠定了坚实的基础。